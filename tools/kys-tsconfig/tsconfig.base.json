{"compilerOptions": {"noEmit": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "allowImportingTsExtensions": true, "moduleDetection": "force", "useDefineForClassFields": true, "jsx": "preserve", "noImplicitThis": true, "strict": true, "verbatimModuleSyntax": true, "target": "ESNext", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}}