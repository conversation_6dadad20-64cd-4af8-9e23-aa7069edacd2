node_modules/

# Compiled output
dist/
build/
out/

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
.temp
.tmp

# Tests
coverage/
test-report/

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# Runtime data
pids
*.pid
*.seed
*.pid.lock

