import { defineComponent } from 'vue';
import { Modal as AModal } from 'ant-design-vue';

const Modal = defineComponent({
  name: 'QModal',
  props: {
    /**
     * Title
     */
    title: {
      type: String,
      required: true,
    },
    /**
     * Visible
     */
    visible: {
      type: Boolean,
      required: false,
    },
  },
  emits: ['click'],
  render() {
    const props = {
      closeable: true,
      visible: this.visible,
      title: this.title,
    };
    // const modalContext = {
    //   props: {
    //     closeable: true,
    //     visible: this.visible,
    //     title: this.title,
    //   },
    // };
    return <AModal {...{ props, attrs: this.$attrs }}></AModal>;
  },
});

export default Modal;
