import { babel } from '@rollup/plugin-babel';
import commonjs from '@rollup/plugin-commonjs';
import resolve from '@rollup/plugin-node-resolve';
import typescript from '@rollup/plugin-typescript';
import url from '@rollup/plugin-url'
import { defineConfig } from 'rollup';
import dts from "rollup-plugin-dts";
import remove from 'rollup-plugin-delete';
import peerDepsExternal from 'rollup-plugin-peer-deps-external';
import postcss from 'rollup-plugin-postcss'
import { globSync } from 'glob';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import postcssUrl from 'postcss-url'

const files = Object.fromEntries(
  globSync('src/**/*.ts').map((file) => [
    path.relative('src', file.slice(0, file.length - path.extname(file).length)),
    fileURLToPath(new URL(file, import.meta.url)),
  ])
);

const extensions = ['.js', '.jsx', '.ts', '.tsx', '.vue'];
const esm = defineConfig({
  plugins: [
    remove({ targets: 'dist/esm' }),
    resolve({ extensions }),
    peerDepsExternal(),
    commonjs({ transformMixedEsModules: true, include: 'node_modules/**' }),
    // 修复 js 文件中的 asset 路径，并转换为 base64
    url({
      limit: 10000000, // 设置一个较大的值，确保所有图片都转为 base64
      include: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg'],
    }),
    // TypeScript
    typescript({
      tsconfig: './tsconfig.lib.json',
      compilerOptions: {
        allowImportingTsExtensions: false,
        noEmit: true,
      },
    }),
    // Babel
    babel({
      extensions,
      babelrc: false,
      babelHelpers: 'bundled', // 'runtime' | 'bundled'
      presets: [
        ['@babel/preset-env', { modules: false }], // 保持 ES6 语法，不转换成 CommonJS
        ['@babel/preset-typescript'],
        ['@vue/babel-preset-jsx'], // Vue 2 JSX 支持
      ],
      exclude: 'node_modules/**'
    }),
    // PostCSS
    postcss({
      extract: 'index.css',
      extensions: ['.css', '.less'],
      inject: false,  // 不自动注入 css 文件
      syntax: 'postcss-less',
      use: {
        less: {
          javascriptEnabled: true,
        },
      },
      plugins: [
        // 将 css 中的图片转换为 base64
        postcssUrl({
          url: 'inline',
          maxSize: 10, // 10MB
          fallback: 'copy',
        }),
      ],
    }),
  ],
  input: files,
  output: {
    dir: 'dist/esm',
    format: 'esm',
  },
});

const types = defineConfig({
  plugins: [
    remove({ targets: 'dist/types' }),
    resolve({ extensions }),
    peerDepsExternal(),
    commonjs({ transformMixedEsModules: true, include: 'node_modules/**' }),
    dts({
      tsconfig: './tsconfig.lib.json',
      respectExternal: true,
      compilerOptions: {
        declaration: true,
        emitDeclarationOnly: true,
      },
    }),
  ],
  input: files,
  output: {
    dir: 'dist/types',
    format: 'esm',
  },
})

export default [esm, types];
